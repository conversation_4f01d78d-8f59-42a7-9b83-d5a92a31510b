// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: signals.sql

package database

import (
	"context"
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

const CreateOrUpdateSignalWithCorrelationID = `-- name: CreateOrUpdateSignalWithCorrelationID :one
WITH ids AS (
    SELECT 
        st.id AS signal_type_id,
        st.isn_id
    FROM signal_types st 
    WHERE st.slug = $4
        AND st.sem_ver = $5
)
INSERT INTO signals (
    id,
    created_at,
    updated_at,
    account_id,
    isn_id,
    signal_type_id,
    local_ref,
    correlation_id,
    is_withdrawn,
    is_archived)
SELECT
    gen_random_uuid(),
    now(),
    now(),
    $1,
    ids.isn_id,
    ids.signal_type_id,
    $2,
    $3,
    false,
    false
FROM ids 
ON CONFLICT (account_id, signal_type_id, local_ref)
DO UPDATE SET
    correlation_id = CASE
        WHEN signals.correlation_id != EXCLUDED.correlation_id THEN EXCLUDED.correlation_id
        ELSE signals.correlation_id
    END,
    is_withdrawn = CASE
        WHEN signals.is_withdrawn = true THEN false
        ELSE signals.is_withdrawn
    END,
    updated_at = now()
RETURNING id
`

type CreateOrUpdateSignalWithCorrelationIDParams struct {
	AccountID      uuid.UUID `json:"account_id"`
	LocalRef       string    `json:"local_ref"`
	CorrelationID  uuid.UUID `json:"correlation_id"`
	SignalTypeSlug string    `json:"signal_type_slug"`
	SemVer         string    `json:"sem_ver"`
}

// note if there is already a master record for this local_ref, then:
// 1. correlation_id is updated with the supplied value (assuming it is different to the existing value)
// 2. if the signal was withdrawn it is reactivated (is_withdrawn = false)
func (q *Queries) CreateOrUpdateSignalWithCorrelationID(ctx context.Context, arg CreateOrUpdateSignalWithCorrelationIDParams) (uuid.UUID, error) {
	row := q.db.QueryRow(ctx, CreateOrUpdateSignalWithCorrelationID,
		arg.AccountID,
		arg.LocalRef,
		arg.CorrelationID,
		arg.SignalTypeSlug,
		arg.SemVer,
	)
	var id uuid.UUID
	err := row.Scan(&id)
	return id, err
}

const CreateSignal = `-- name: CreateSignal :one
WITH ids AS (
    SELECT st.id AS signal_type_id, 
        st.isn_id,
        gen_random_uuid() AS signal_id
    FROM signal_types st 
    WHERE st.slug = $3
        AND st.sem_ver = $4
)
INSERT INTO signals (
    id,
    created_at,
    updated_at,
    account_id,
    isn_id,
    signal_type_id,
    local_ref,
    correlation_id,
    is_withdrawn,
    is_archived)
SELECT
    ids.signal_id,
    now(),
    now(),
    $1,
    ids.isn_id,
    ids.signal_type_id,
    $2,
    ids.signal_id,
    false,
    false
FROM ids
ON CONFLICT (account_id, signal_type_id, local_ref)
DO UPDATE SET
    is_withdrawn = false,
    updated_at = CASE 
        WHEN signals.is_withdrawn = true THEN now()
        ELSE signals.updated_at
    END
RETURNING id
`

type CreateSignalParams struct {
	AccountID      uuid.UUID `json:"account_id"`
	LocalRef       string    `json:"local_ref"`
	SignalTypeSlug string    `json:"signal_type_slug"`
	SemVer         string    `json:"sem_ver"`
}

// this query creates one row in the signals table for every new combination of account_id, signal_type_id, local_ref.
// if a withdrawn signal is received again it is reactivated (is_withdrawn = false).
// returns the signal_id.
// note the only way to update a singlas record is to change the is_withdrawn status
// records are reactivated by resubmitting them, so this update ensures the updated_at timestamp is only changed if the record is reactivated
func (q *Queries) CreateSignal(ctx context.Context, arg CreateSignalParams) (uuid.UUID, error) {
	row := q.db.QueryRow(ctx, CreateSignal,
		arg.AccountID,
		arg.LocalRef,
		arg.SignalTypeSlug,
		arg.SemVer,
	)
	var id uuid.UUID
	err := row.Scan(&id)
	return id, err
}

const CreateSignalVersion = `-- name: CreateSignalVersion :one
WITH ver AS (
    SELECT 
        st.id AS signal_type_id,
        COALESCE(
            (SELECT MAX(sv.version_number) 
             FROM signal_versions sv 
             JOIN signals s
                ON s.id = sv.signal_id
             WHERE s.local_ref = $4)
            , 0) + 1 as version_number
    FROM signal_types st
    WHERE st.slug = $5
        AND st.sem_ver = $6
)
INSERT INTO signal_versions (
    id,
    created_at,
    account_id,
    signal_batch_id,
    signal_id,
    version_number,
    content
)
SELECT
    gen_random_uuid(),
    now(), 
    $1,
    $2,
    s.id,
    ver.version_number,
    $3
FROM ver 
JOIN signals s 
    ON s.signal_type_id = ver.signal_type_id
    AND s.account_id = $1
    AND s.local_ref = $4
RETURNING id, version_number
`

type CreateSignalVersionParams struct {
	AccountID      uuid.UUID       `json:"account_id"`
	SignalBatchID  uuid.UUID       `json:"signal_batch_id"`
	Content        json.RawMessage `json:"content"`
	LocalRef       string          `json:"local_ref"`
	SignalTypeSlug string          `json:"signal_type_slug"`
	SemVer         string          `json:"sem_ver"`
}

type CreateSignalVersionRow struct {
	ID            uuid.UUID `json:"id"`
	VersionNumber int32     `json:"version_number"`
}

// if there is already a version of this signal, create a new one with an incremented version_number
func (q *Queries) CreateSignalVersion(ctx context.Context, arg CreateSignalVersionParams) (CreateSignalVersionRow, error) {
	row := q.db.QueryRow(ctx, CreateSignalVersion,
		arg.AccountID,
		arg.SignalBatchID,
		arg.Content,
		arg.LocalRef,
		arg.SignalTypeSlug,
		arg.SemVer,
	)
	var i CreateSignalVersionRow
	err := row.Scan(&i.ID, &i.VersionNumber)
	return i, err
}

const GetLatestSignalVersionsWithOptionalFilters = `-- name: GetLatestSignalVersionsWithOptionalFilters :many
WITH LatestSignals AS (
    SELECT
        a.id AS account_id,
        a.account_type,
        COALESCE(u.email, si.client_contact_email) AS email, -- show either the user or service account email
        s.local_ref,
        sv.version_number,
        sv.created_at,
        sv.id AS signal_version_id,
        sv.signal_id,
        s2.local_ref AS correlated_local_ref,
        s2.id AS correlated_signal_id,
        s.is_withdrawn,
        sv.content,
        ROW_NUMBER() OVER (PARTITION BY sv.signal_id ORDER BY sv.version_number DESC) AS rn
    FROM
        signal_versions sv
    JOIN
        signals s ON s.id = sv.signal_id
    JOIN
        signals s2 ON s2.id = s.correlation_id
    JOIN 
        accounts a ON a.id = s.account_id
    JOIN 
        signal_types st on st.id = s.signal_type_id
    JOIN 
        isn i ON i.id = st.isn_id 
    LEFT OUTER JOIN 
        users u ON u.account_id = a.id
    LEFT OUTER JOIN 
        service_accounts si ON si.account_id = a.id
    WHERE i.slug = $1
        AND st.slug = $2
        AND st.sem_ver = $3
        AND i.is_in_use = true
        AND st.is_in_use = true
        AND ($4::boolean = true OR s.is_withdrawn = false)
        AND ($5::uuid IS NULL OR a.id = $5::uuid)
        AND ($6::uuid IS NULL OR s.id = $6::uuid)
        AND ($7::text IS NULL OR s.local_ref = $7::text)
        AND ($8::timestamptz IS NULL OR sv.created_at >= $8::timestamptz)
        AND ($9::timestamptz IS NULL OR sv.created_at <= $9::timestamptz)
)
SELECT
    ls.account_id,
    ls.account_type,
    ls.email,
    ls.local_ref,
    ls.version_number,
    ls.created_at,
    ls.signal_version_id,
    ls.signal_id,
    ls.correlated_local_ref,
    ls.correlated_signal_id,
    ls.is_withdrawn,
    ls.content
FROM
    LatestSignals ls
WHERE
    ls.rn = 1
ORDER BY
    ls.local_ref,
    ls.version_number,
    ls.signal_version_id
`

type GetLatestSignalVersionsWithOptionalFiltersParams struct {
	IsnSlug          string     `json:"isn_slug"`
	SignalTypeSlug   string     `json:"signal_type_slug"`
	SemVer           string     `json:"sem_ver"`
	IncludeWithdrawn *bool      `json:"include_withdrawn"`
	AccountID        *uuid.UUID `json:"account_id"`
	SignalID         *uuid.UUID `json:"signal_id"`
	LocalRef         *string    `json:"local_ref"`
	StartDate        *time.Time `json:"start_date"`
	EndDate          *time.Time `json:"end_date"`
}

type GetLatestSignalVersionsWithOptionalFiltersRow struct {
	AccountID          uuid.UUID        `json:"account_id"`
	AccountType        string           `json:"account_type"`
	Email              string           `json:"email"`
	LocalRef           string           `json:"local_ref"`
	VersionNumber      int32            `json:"version_number"`
	CreatedAt          time.Time        `json:"created_at"`
	SignalVersionID    uuid.UUID        `json:"signal_version_id"`
	SignalID           uuid.UUID        `json:"signal_id"`
	CorrelatedLocalRef string           `json:"correlated_local_ref"`
	CorrelatedSignalID uuid.UUID        `json:"correlated_signal_id"`
	IsWithdrawn        bool             `json:"is_withdrawn"`
	Content            json.RawMessage  `json:"content"`
	CorrelatedContent  *json.RawMessage `json:"correlated_content"`
}

// Note the get queries:
// require isn_slug,signal_type_slug & sem_ver params
func (q *Queries) GetLatestSignalVersionsWithOptionalFilters(ctx context.Context, arg GetLatestSignalVersionsWithOptionalFiltersParams) ([]GetLatestSignalVersionsWithOptionalFiltersRow, error) {
	rows, err := q.db.Query(ctx, GetLatestSignalVersionsWithOptionalFilters,
		arg.IsnSlug,
		arg.SignalTypeSlug,
		arg.SemVer,
		arg.IncludeWithdrawn,
		arg.AccountID,
		arg.SignalID,
		arg.LocalRef,
		arg.StartDate,
		arg.EndDate,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetLatestSignalVersionsWithOptionalFiltersRow
	for rows.Next() {
		var i GetLatestSignalVersionsWithOptionalFiltersRow
		if err := rows.Scan(
			&i.AccountID,
			&i.AccountType,
			&i.Email,
			&i.LocalRef,
			&i.VersionNumber,
			&i.CreatedAt,
			&i.SignalVersionID,
			&i.SignalID,
			&i.CorrelatedLocalRef,
			&i.CorrelatedSignalID,
			&i.IsWithdrawn,
			&i.Content,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetSignalByAccountAndLocalRef = `-- name: GetSignalByAccountAndLocalRef :one
SELECT s.id, s.created_at, s.updated_at, s.account_id, s.isn_id, s.signal_type_id, s.local_ref, s.correlation_id, s.is_withdrawn, s.is_archived, i.slug as isn_slug, st.slug as signal_type_slug, st.sem_ver
FROM signals s
JOIN signal_types st ON st.id = s.signal_type_id
JOIN isn i ON i.id = s.isn_id
WHERE s.account_id = $1
    AND st.slug = $2
    AND st.sem_ver = $3
    AND s.local_ref = $4
`

type GetSignalByAccountAndLocalRefParams struct {
	AccountID uuid.UUID `json:"account_id"`
	Slug      string    `json:"slug"`
	SemVer    string    `json:"sem_ver"`
	LocalRef  string    `json:"local_ref"`
}

type GetSignalByAccountAndLocalRefRow struct {
	ID             uuid.UUID `json:"id"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
	AccountID      uuid.UUID `json:"account_id"`
	IsnID          uuid.UUID `json:"isn_id"`
	SignalTypeID   uuid.UUID `json:"signal_type_id"`
	LocalRef       string    `json:"local_ref"`
	CorrelationID  uuid.UUID `json:"correlation_id"`
	IsWithdrawn    bool      `json:"is_withdrawn"`
	IsArchived     bool      `json:"is_archived"`
	IsnSlug        string    `json:"isn_slug"`
	SignalTypeSlug string    `json:"signal_type_slug"`
	SemVer         string    `json:"sem_ver"`
}

func (q *Queries) GetSignalByAccountAndLocalRef(ctx context.Context, arg GetSignalByAccountAndLocalRefParams) (GetSignalByAccountAndLocalRefRow, error) {
	row := q.db.QueryRow(ctx, GetSignalByAccountAndLocalRef,
		arg.AccountID,
		arg.Slug,
		arg.SemVer,
		arg.LocalRef,
	)
	var i GetSignalByAccountAndLocalRefRow
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.AccountID,
		&i.IsnID,
		&i.SignalTypeID,
		&i.LocalRef,
		&i.CorrelationID,
		&i.IsWithdrawn,
		&i.IsArchived,
		&i.IsnSlug,
		&i.SignalTypeSlug,
		&i.SemVer,
	)
	return i, err
}

const GetSignalCorrelationDetails = `-- name: GetSignalCorrelationDetails :one
SELECT 
    s.id,
    s.local_ref,
    s.correlation_id,
    s.isn_id,
    i.slug as isn_slug,
    sc.local_ref as correlated_local_ref,
    sc.id as correlated_signal_id
FROM signals s
JOIN signal_types st ON st.id = s.signal_type_id
JOIN isn i ON i.id = s.isn_id
join signals sc on sc.id = s.correlation_id
WHERE s.account_id = $1
    AND st.slug = $2
    AND st.sem_ver = $3
    AND s.local_ref = $4
`

type GetSignalCorrelationDetailsParams struct {
	AccountID uuid.UUID `json:"account_id"`
	Slug      string    `json:"slug"`
	SemVer    string    `json:"sem_ver"`
	LocalRef  string    `json:"local_ref"`
}

type GetSignalCorrelationDetailsRow struct {
	ID                 uuid.UUID `json:"id"`
	LocalRef           string    `json:"local_ref"`
	CorrelationID      uuid.UUID `json:"correlation_id"`
	IsnID              uuid.UUID `json:"isn_id"`
	IsnSlug            string    `json:"isn_slug"`
	CorrelatedLocalRef string    `json:"correlated_local_ref"`
	CorrelatedSignalID uuid.UUID `json:"correlated_signal_id"`
}

// Get signal with its correlation details for verification
func (q *Queries) GetSignalCorrelationDetails(ctx context.Context, arg GetSignalCorrelationDetailsParams) (GetSignalCorrelationDetailsRow, error) {
	row := q.db.QueryRow(ctx, GetSignalCorrelationDetails,
		arg.AccountID,
		arg.Slug,
		arg.SemVer,
		arg.LocalRef,
	)
	var i GetSignalCorrelationDetailsRow
	err := row.Scan(
		&i.ID,
		&i.LocalRef,
		&i.CorrelationID,
		&i.IsnID,
		&i.IsnSlug,
		&i.CorrelatedLocalRef,
		&i.CorrelatedSignalID,
	)
	return i, err
}

const ValidateCorrelationID = `-- name: ValidateCorrelationID :one
SELECT EXISTS(
    SELECT 1
    FROM signals s
    JOIN signal_types st ON st.id = s.signal_type_id
    JOIN isn i ON i.id = st.isn_id
    WHERE s.id = $1
        AND i.slug = $2
) AS is_valid
`

type ValidateCorrelationIDParams struct {
	CorrelationID uuid.UUID `json:"correlation_id"`
	IsnSlug       string    `json:"isn_slug"`
}

func (q *Queries) ValidateCorrelationID(ctx context.Context, arg ValidateCorrelationIDParams) (bool, error) {
	row := q.db.QueryRow(ctx, ValidateCorrelationID, arg.CorrelationID, arg.IsnSlug)
	var is_valid bool
	err := row.Scan(&is_valid)
	return is_valid, err
}

const WithdrawSignalByID = `-- name: WithdrawSignalByID :execrows
UPDATE signals
SET is_withdrawn = true, updated_at = NOW()
WHERE id = $1
`

func (q *Queries) WithdrawSignalByID(ctx context.Context, id uuid.UUID) (int64, error) {
	result, err := q.db.Exec(ctx, WithdrawSignalByID, id)
	if err != nil {
		return 0, err
	}
	return result.RowsAffected(), nil
}

const WithdrawSignalByLocalRef = `-- name: WithdrawSignalByLocalRef :execrows
UPDATE signals
SET is_withdrawn = true, updated_at = NOW()
WHERE account_id = $1
    AND signal_type_id = (
        SELECT st.id
        FROM signal_types st
        WHERE st.slug = $2 AND st.sem_ver = $3
    )
    AND local_ref = $4
`

type WithdrawSignalByLocalRefParams struct {
	AccountID uuid.UUID `json:"account_id"`
	Slug      string    `json:"slug"`
	SemVer    string    `json:"sem_ver"`
	LocalRef  string    `json:"local_ref"`
}

func (q *Queries) WithdrawSignalByLocalRef(ctx context.Context, arg WithdrawSignalByLocalRefParams) (int64, error) {
	result, err := q.db.Exec(ctx, WithdrawSignalByLocalRef,
		arg.AccountID,
		arg.Slug,
		arg.SemVer,
		arg.LocalRef,
	)
	if err != nil {
		return 0, err
	}
	return result.RowsAffected(), nil
}
